package com.sqx.modules.healthRecord.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.entity.HealthStatusRecord;
import com.sqx.modules.healthRecord.dao.HealthStatusRecordDao;
import com.sqx.modules.healthRecord.service.HealthStatusRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.modules.patientInfo.entity.PatientInfo;
import com.sqx.modules.patientInfo.service.PatientInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * <p>
 * 健康状况记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class HealthStatusRecordServiceImpl extends ServiceImpl<HealthStatusRecordDao, HealthStatusRecord> implements HealthStatusRecordService {

    @Autowired
    private HealthStatusRecordDao healthStatusRecordDao;

    @Autowired
    private PatientInfoService patientInfoService;

    @Override
    public Result saveHealthRecord(HealthStatusRecord healthRecord) {
        // 验证患者是否存在
        if (healthRecord.getPatientId() != null) {
            PatientInfo patientInfo = patientInfoService.getPatientInfo(healthRecord.getPatientId());
            if (patientInfo == null) {
                return Result.error("患者信息不存在");
            }
            // 设置用户ID
            healthRecord.setUserId(patientInfo.getUserId());
        }

        // 自动计算BMI
        if (healthRecord.getWeight() != null && healthRecord.getHeight() != null 
            && healthRecord.getWeight().compareTo(BigDecimal.ZERO) > 0 
            && healthRecord.getHeight().compareTo(BigDecimal.ZERO) > 0) {
            
            // BMI = 体重(kg) / (身高(m))^2
            BigDecimal heightInMeters = healthRecord.getHeight().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
            BigDecimal bmi = healthRecord.getWeight().divide(
                heightInMeters.multiply(heightInMeters), 2, RoundingMode.HALF_UP);
            healthRecord.setBmi(bmi);
        }

        // 设置默认值
        if (healthRecord.getRecordType() == null) {
            healthRecord.setRecordType(1); // 默认为日常记录
        }
        if (healthRecord.getHealthStatus() == null) {
            healthRecord.setHealthStatus(2); // 默认为良好
        }
        if (healthRecord.getRiskAssessment() == null) {
            healthRecord.setRiskAssessment(1); // 默认为低风险
        }
        if (healthRecord.getDataSource() == null) {
            healthRecord.setDataSource(1); // 默认为手动录入
        }
        if (healthRecord.getIsAbnormal() == null) {
            healthRecord.setIsAbnormal(0); // 默认为正常
        }
        if (healthRecord.getIsDelete() == null) {
            healthRecord.setIsDelete(0); // 默认为未删除
        }

        if (healthRecord.getRecordId() == null) {
            // 新增
            healthRecord.setCreateTime(LocalDateTime.now());
            healthRecord.setUpdateTime(LocalDateTime.now());
            return Result.upStatus(healthStatusRecordDao.insert(healthRecord));
        } else {
            // 修改
            healthRecord.setUpdateTime(LocalDateTime.now());
            return Result.upStatus(healthStatusRecordDao.updateById(healthRecord));
        }
    }

    @Override
    public IPage<HealthStatusRecord> getHealthRecordList(Integer page, Integer limit, HealthStatusRecord healthRecord) {
        Page<HealthStatusRecord> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return healthStatusRecordDao.getHealthRecordList(pages, healthRecord);
    }

    @Override
    public HealthStatusRecord getHealthRecordInfo(Long recordId) {
        return healthStatusRecordDao.selectById(recordId);
    }

    @Override
    public IPage<HealthStatusRecord> getHealthRecordsByPatientId(Integer page, Integer limit, Long patientId) {
        Page<HealthStatusRecord> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return healthStatusRecordDao.getHealthRecordsByPatientId(pages, patientId);
    }

    @Override
    public IPage<HealthStatusRecord> getHealthRecordsByUserId(Integer page, Integer limit, Long userId) {
        Page<HealthStatusRecord> pages;
        if (page != null && limit != null) {
            pages = new Page<>(page, limit);
        } else {
            pages = new Page<>();
            pages.setSize(-1);
        }
        return healthStatusRecordDao.getHealthRecordsByUserId(pages, userId);
    }

    @Override
    public Result deleteHealthRecord(Long recordId, Long operatorId) {
        HealthStatusRecord healthRecord = healthStatusRecordDao.selectById(recordId);
        if (healthRecord == null) {
            return Result.error("健康记录不存在");
        }
        
        healthRecord.setIsDelete(1);
        healthRecord.setUpdatedBy(operatorId);
        healthRecord.setUpdateTime(LocalDateTime.now());
        
        return Result.upStatus(healthStatusRecordDao.updateById(healthRecord));
    }

    @Override
    public Result batchDeleteHealthRecords(Long[] recordIds, Long operatorId) {
        if (recordIds == null || recordIds.length == 0) {
            return Result.error("请选择要删除的记录");
        }

        for (Long recordId : recordIds) {
            Result result = deleteHealthRecord(recordId, operatorId);
            if ((int) result.get("code") != 0) {
                return result;
            }
        }
        
        return Result.success("批量删除成功");
    }
}
