<template>
	<div>
		<el-card class="box-card">
			<div slot="header" class="clearfix">
				<span>健康记录详情</span>
				<el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
			</div>

			<div v-loading="loading">
				<!-- 基本信息 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">基本信息</h4>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">患者姓名：</span>
								<span class="value">{{ healthRecord.patientName || '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">记录日期：</span>
								<span class="value">{{ healthRecord.recordDate || '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">记录时间：</span>
								<span class="value">{{ healthRecord.recordTime || '-' }}</span>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">记录类型：</span>
								<span class="value">{{ getRecordTypeText(healthRecord.recordType) }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">健康状态：</span>
								<span class="value">{{ getHealthStatusText(healthRecord.healthStatus) }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">风险评估：</span>
								<span class="value">{{ getRiskAssessmentText(healthRecord.riskAssessment) }}</span>
							</div>
						</el-col>
					</el-row>
				</div>

				<!-- 生理指标 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">生理指标</h4>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">体重：</span>
								<span class="value">{{ healthRecord.weight ? healthRecord.weight + ' kg' : '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">身高：</span>
								<span class="value">{{ healthRecord.height ? healthRecord.height + ' cm' : '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">BMI：</span>
								<span class="value">{{ healthRecord.bmi || '-' }}</span>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">血压：</span>
								<span class="value">
									{{ healthRecord.bloodPressureSystolic && healthRecord.bloodPressureDiastolic 
										? healthRecord.bloodPressureSystolic + '/' + healthRecord.bloodPressureDiastolic + ' mmHg' 
										: '-' }}
								</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">心率：</span>
								<span class="value">{{ healthRecord.heartRate ? healthRecord.heartRate + ' 次/分' : '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">体温：</span>
								<span class="value">{{ healthRecord.bodyTemperature ? healthRecord.bodyTemperature + ' ℃' : '-' }}</span>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">血糖：</span>
								<span class="value">{{ healthRecord.bloodSugar ? healthRecord.bloodSugar + ' mmol/L' : '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">疼痛等级：</span>
								<span class="value">{{ healthRecord.painLevel !== null ? healthRecord.painLevel + ' 级' : '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">是否异常：</span>
								<el-tag :type="healthRecord.isAbnormal === 1 ? 'danger' : 'success'">
									{{ healthRecord.isAbnormal === 1 ? '异常' : '正常' }}
								</el-tag>
							</div>
						</el-col>
					</el-row>
				</div>

				<!-- 生活方式 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">生活方式</h4>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">运动时长：</span>
								<span class="value">{{ healthRecord.exerciseDuration ? healthRecord.exerciseDuration + ' 分钟' : '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">运动类型：</span>
								<span class="value">{{ healthRecord.exerciseType || '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">睡眠时长：</span>
								<span class="value">{{ healthRecord.sleepHours ? healthRecord.sleepHours + ' 小时' : '-' }}</span>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">睡眠质量：</span>
								<span class="value">{{ getSleepQualityText(healthRecord.sleepQuality) }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">情绪状态：</span>
								<span class="value">{{ getMoodStatusText(healthRecord.moodStatus) }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">用药依从性：</span>
								<span class="value">{{ getMedicationAdherenceText(healthRecord.medicationAdherence) }}</span>
							</div>
						</el-col>
					</el-row>
				</div>

				<!-- 详细信息 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">详细信息</h4>

					<el-row :gutter="20">
						<el-col :span="12">
							<div class="detail-item">
								<span class="label">症状描述：</span>
								<div class="value textarea-value">{{ healthRecord.symptoms || '-' }}</div>
							</div>
						</el-col>
						<el-col :span="12">
							<div class="detail-item">
								<span class="label">疼痛部位：</span>
								<div class="value textarea-value">{{ healthRecord.painLocation || '-' }}</div>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="12">
							<div class="detail-item">
								<span class="label">用药情况：</span>
								<div class="value textarea-value">{{ healthRecord.medicationTaken || '-' }}</div>
							</div>
						</el-col>
						<el-col :span="12">
							<div class="detail-item">
								<span class="label">饮食记录：</span>
								<div class="value textarea-value">{{ healthRecord.dietNotes || '-' }}</div>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="12">
							<div class="detail-item">
								<span class="label">特殊事件：</span>
								<div class="value textarea-value">{{ healthRecord.specialEvents || '-' }}</div>
							</div>
						</el-col>
						<el-col :span="12">
							<div class="detail-item">
								<span class="label">医生建议：</span>
								<div class="value textarea-value">{{ healthRecord.doctorAdvice || '-' }}</div>
							</div>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">下次随访日期：</span>
								<span class="value">{{ healthRecord.nextFollowupDate || '-' }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">数据来源：</span>
								<span class="value">{{ getDataSourceText(healthRecord.dataSource) }}</span>
							</div>
						</el-col>
						<el-col :span="8">
							<div class="detail-item">
								<span class="label">创建时间：</span>
								<span class="value">{{ healthRecord.createTime || '-' }}</span>
							</div>
						</el-col>
					</el-row>
				</div>

				<div style="text-align: center; margin-top: 30px;">
					<el-button @click="goBack">返 回</el-button>
					<el-button type="primary" @click="editRecord">编 辑</el-button>
				</div>
			</div>
		</el-card>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				loading: false,
				healthRecord: {},
				recordTypeList: [],
				sleepQualityList: [],
				moodStatusList: [],
				medicationAdherenceList: [],
				dataSourceList: []
			}
		},
		methods: {
			// 获取健康记录详情
			getHealthRecordInfo(recordId) {
				this.loading = true
				this.$http({
					url: this.$http.adornUrl('admin/healthRecord/getHealthRecordInfo'),
					method: 'get',
					params: this.$http.adornParams({ recordId: recordId })
				}).then(({ data }) => {
					this.loading = false
					if (data && data.code === 0) {
						this.healthRecord = data.data
					}
				}).catch(() => {
					this.loading = false
				})
			},

			// 获取字典数据
			getDictData() {
				// 获取记录类型
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '记录类型' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.recordTypeList = data.data
					}
				})

				// 获取睡眠质量
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '睡眠质量' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.sleepQualityList = data.data
					}
				})

				// 获取情绪状态
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '情绪状态' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.moodStatusList = data.data
					}
				})

				// 获取用药依从性
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '用药依从性' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.medicationAdherenceList = data.data
					}
				})

				// 获取数据来源
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '数据来源' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.dataSourceList = data.data
					}
				})
			},

			// 返回列表
			goBack() {
				this.$router.go(-1)
			},

			// 编辑记录
			editRecord() {
				this.$router.push({
					path: '/healthRecordEdit',
					query: {
						recordId: this.healthRecord.recordId
					}
				})
			},

			// 获取记录类型文本
			getRecordTypeText(code) {
				if (!code) return '-'
				const item = this.recordTypeList.find(item => item.code == code)
				return item ? item.value : '-'
			},

			// 获取健康状态文本
			getHealthStatusText(code) {
				const statusMap = {
					1: '优秀',
					2: '良好',
					3: '一般',
					4: '较差',
					5: '差'
				}
				return statusMap[code] || '-'
			},

			// 获取风险评估文本
			getRiskAssessmentText(code) {
				const riskMap = {
					1: '低风险',
					2: '中风险',
					3: '高风险'
				}
				return riskMap[code] || '-'
			},

			// 获取睡眠质量文本
			getSleepQualityText(code) {
				if (!code) return '-'
				const item = this.sleepQualityList.find(item => item.code == code)
				return item ? item.value : '-'
			},

			// 获取情绪状态文本
			getMoodStatusText(code) {
				if (!code) return '-'
				const item = this.moodStatusList.find(item => item.code == code)
				return item ? item.value : '-'
			},

			// 获取用药依从性文本
			getMedicationAdherenceText(code) {
				if (!code) return '-'
				const item = this.medicationAdherenceList.find(item => item.code == code)
				return item ? item.value : '-'
			},

			// 获取数据来源文本
			getDataSourceText(code) {
				if (!code) return '-'
				const item = this.dataSourceList.find(item => item.code == code)
				return item ? item.value : '-'
			}
		},

		mounted() {
			const recordId = this.$route.query.recordId
			if (recordId) {
				this.getHealthRecordInfo(recordId)
			}
			this.getDictData()
		}
	}
</script>

<style scoped>
	.detail-item {
		margin-bottom: 15px;
	}

	.label {
		font-weight: bold;
		color: #606266;
		display: inline-block;
		width: 120px;
	}

	.value {
		color: #303133;
	}

	.textarea-value {
		margin-top: 5px;
		padding: 8px;
		background-color: #f5f7fa;
		border-radius: 4px;
		min-height: 60px;
		white-space: pre-wrap;
		word-break: break-all;
	}
</style>
