<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="就诊人列表" name="first">
				<div style="display: flex;justify-content: space-between;align-items: center">
					<div>
						<!-- <div style="position: relative;display: inline-block;margin: 3px;">
							<span>就诊人名称：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入就诊人名称"
								v-model="realNameT"></el-input>&nbsp;&nbsp;
						</div> -->
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>所属用户昵称：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入所属用户昵称"
								v-model="userName"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>就诊人电话：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入就诊人电话"
								v-model="phone"></el-input>&nbsp;&nbsp;
						</div>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="select">
							查询
						</el-button>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleans">
							重置
						</el-button>
						<el-button style='margin: 10px 0;' :disabled="!isAuth('carBrandList:add')" size="mini"
							type="primary" icon="document" @click="addPatient()">添加就诊人
						</el-button>
					</div>
				</div>
				<el-table v-loading="tableDataLoading" :data="tableData.records">
					<el-table-column fixed prop="patientId" label="编号" width="80">
					</el-table-column>
					<el-table-column prop="userName" label="所属用户昵称">
						<template slot-scope="scope">
							<!-- <span style="color: #009900;cursor: pointer;"
								@click="updates(scope.row.userId)">{{ scope.row.userName }}</span> -->
							<el-button size="mini" style="color: #008000;background: #fff;border: none;padding: 0;" type="primary"
								:disabled="!isAuth('userList:details')" @click="updates(scope.row.userId)">
								{{scope.row.userName ? scope.row.userName : '未绑定'}}
							</el-button>
						</template>
					</el-table-column>
					<el-table-column prop="userName" label="就诊人名称">
						<template slot-scope="scope">
							<span>{{ scope.row.realName }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="userName" label="性别">
						<template slot-scope="scope">
							<span>{{ scope.row.sex == 1 ? '男' : '女' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="isUnderAge" label="是否满18岁">
						<template slot-scope="scope">
							<span>{{ scope.row.isUnderAge == 1 ? '是' : '否' }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="relationship" label="与就诊人关系">
					</el-table-column>
					<el-table-column prop="phone" label="就诊人电话">
					</el-table-column>
					<el-table-column prop="idNumber" label="身份证号码">
					</el-table-column>
					<el-table-column prop="emergencyPhone" label="紧急联系人电话">
					</el-table-column>
					<el-table-column prop="birthDate" label="出生日期">
					</el-table-column>
					<el-table-column prop="maritalStatus" label="婚姻状况">
						<template slot-scope="scope">
							<span>{{ getMaritalStatusText(scope.row.maritalStatus) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="insuranceType" label="医保类型">
						<template slot-scope="scope">
							<span>{{ getInsuranceTypeText(scope.row.insuranceType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="riskLevel" label="风险等级">
						<template slot-scope="scope">
							<span>{{ getRiskLevelText(scope.row.riskLevel) }}</span>
						</template>
					</el-table-column>

					<el-table-column label="操作" width="280">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" :disabled="!isAuth('carBrandList:update')"
								@click="addPatient(scope.row)" style="margin: 5px;">修改
							</el-button>
							<el-button size="mini" type="danger" :disabled="!isAuth('carBrandList:delete')"
								@click="deletes(scope.row)" style="margin: 5px;">删除
							</el-button>
							<el-button size="mini" type="success" 
								@click="viewHealthRecords(scope.row)" style="margin: 5px;">健康记录
							</el-button>
						</template>
					</el-table-column>
				</el-table>
				<div style="text-align: center;margin-top: 10px;">
					<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="limit" :current-page="page"
						layout="total,sizes, prev, pager, next,jumper" :total="tableData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
		</el-tabs>
		<!--添加就诊人-->
		<el-dialog :title="titles" :visible.sync="dialogVisible" width="80%" center>
			<!-- 基本信息 -->
			<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
				<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">基本信息</h4>

				<!-- 第一行：所属用户、姓名、性别 -->
				<div style="display: flex; margin-bottom: 15px; align-items: center;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">所属用户：</span>
						<el-input v-model="numberValidateForm.userName" @focus="userBtn()" style="width: 200px;" placeholder="请选择用户"></el-input>
					</div>
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">姓名：</span>
						<el-input v-model="numberValidateForm.realName" style="width: 200px;" placeholder="请输入姓名"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">性别：</span>
						<el-radio-group v-model="numberValidateForm.sex">
							<el-radio :label="1">男</el-radio>
							<el-radio :label="2">女</el-radio>
						</el-radio-group>
					</div>
				</div>

				<!-- 第二行：出生日期、民族、是否满18岁 -->
				<div style="display: flex; margin-bottom: 15px; align-items: center;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">出生日期：</span>
						<el-date-picker v-model="numberValidateForm.birthDate" type="date" placeholder="请选择出生日期"
							style="width: 200px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
					</div>
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">民族：</span>
						<el-input v-model="numberValidateForm.ethnicity" style="width: 200px;" placeholder="请输入民族"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">是否满18岁：</span>
						<el-radio-group v-model="numberValidateForm.isUnderAge">
							<el-radio :label="1">是</el-radio>
							<el-radio :label="0">否</el-radio>
						</el-radio-group>
					</div>
				</div>

				<!-- 第三行：身份证号码、婚姻状况、就诊人关系 -->
				<div style="display: flex; margin-bottom: 15px; align-items: center;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">身份证号码：</span>
						<el-input v-model="numberValidateForm.idNumber" style="width: 200px;" placeholder="请输入身份证号码"></el-input>
					</div>
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">婚姻状况：</span>
						<el-select v-model="numberValidateForm.maritalStatus" placeholder="请选择婚姻状况" style="width: 200px;">
							<el-option v-for="(item,index) in maritalStatusList" :key="index" :label="item.value" :value="item.code"></el-option>
						</el-select>
					</div>
					<div style="flex: 1;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">就诊人关系：</span>
						<el-select v-model="numberValidateForm.relationship" placeholder="请选择就诊人关系" style="width: 200px;">
							<el-option v-for="(item,index) in gxList" :key="index" :label="item.value" :value="item.value"></el-option>
						</el-select>
					</div>
				</div>
			</div>

			<!-- 联系信息 -->
			<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
				<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">联系信息</h4>

				<!-- 第一行：电话号码、紧急联系人电话 -->
				<div style="display: flex; margin-bottom: 15px; align-items: center;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">电话号码：</span>
						<el-input v-model="numberValidateForm.phone" style="width: 200px;" placeholder="请输入电话号码"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 120px; display: inline-block; text-align: right; margin-right: 10px;">紧急联系人电话：</span>
						<el-input v-model="numberValidateForm.emergencyPhone" style="width: 200px;" placeholder="请输入紧急联系人电话"></el-input>
					</div>
				</div>

				<!-- 第二行：现住址 -->
				<div style="display: flex; margin-bottom: 15px; align-items: flex-start;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">现住址：</span>
						<el-input v-model="numberValidateForm.currentAddress" type="textarea" :rows="2"
							style="width: calc(100% - 120px);" placeholder="请输入现住址"></el-input>
					</div>
				</div>

				<!-- 第三行：户籍地址 -->
				<div style="display: flex; margin-bottom: 0; align-items: flex-start;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">户籍地址：</span>
						<el-input v-model="numberValidateForm.registeredAddress" type="textarea" :rows="2"
							style="width: calc(100% - 120px);" placeholder="请输入户籍地址"></el-input>
					</div>
				</div>
			</div>

			<!-- 工作信息 -->
			<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
				<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">工作信息</h4>

				<!-- 职业、工作单位 -->
				<div style="display: flex; margin-bottom: 0; align-items: center;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">职业：</span>
						<el-input v-model="numberValidateForm.occupation" style="width: 200px;" placeholder="请输入职业"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">工作单位：</span>
						<el-input v-model="numberValidateForm.employer" style="width: 300px;" placeholder="请输入工作单位"></el-input>
					</div>
				</div>
			</div>

			<!-- 医保信息 -->
			<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
				<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">医保信息</h4>

				<!-- 医保类型、医保卡号、风险等级 -->
				<div style="display: flex; margin-bottom: 0; align-items: center;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">医保类型：</span>
						<el-select v-model="numberValidateForm.insuranceType" placeholder="请选择医保类型" style="width: 200px;">
							<el-option v-for="(item,index) in insuranceTypeList" :key="index" :label="item.value" :value="item.code"></el-option>
						</el-select>
					</div>
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">医保卡号：</span>
						<el-input v-model="numberValidateForm.insuranceNumber" style="width: 200px;" placeholder="请输入医保卡号"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 80px; display: inline-block; text-align: right; margin-right: 10px;">风险等级：</span>
						<el-select v-model="numberValidateForm.riskLevel" placeholder="请选择风险等级" style="width: 200px;">
							<el-option v-for="(item,index) in riskLevelList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
						</el-select>
					</div>
				</div>
			</div>

			<!-- 医疗信息 -->
			<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
				<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">医疗信息</h4>

				<!-- 第一行：既往重大疾病史、药物过敏史 -->
				<div style="display: flex; margin-bottom: 15px; align-items: flex-start;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 120px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">既往重大疾病史：</span>
						<el-input v-model="numberValidateForm.majorMedicalHistory" type="textarea" :rows="3"
							style="width: calc(100% - 140px);" placeholder="请输入既往重大疾病史摘要"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">药物过敏史：</span>
						<el-input v-model="numberValidateForm.drugAllergySummary" type="textarea" :rows="3"
							style="width: calc(100% - 120px);" placeholder="请输入药物过敏史摘要"></el-input>
					</div>
				</div>

				<!-- 第二行：当前主要用药、特殊医疗需求 -->
				<div style="display: flex; margin-bottom: 0; align-items: flex-start;">
					<div style="flex: 1; margin-right: 20px;">
						<span style="width: 120px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">当前主要用药：</span>
						<el-input v-model="numberValidateForm.currentMedicationSummary" type="textarea" :rows="3"
							style="width: calc(100% - 140px);" placeholder="请输入当前主要用药摘要"></el-input>
					</div>
					<div style="flex: 1;">
						<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">特殊医疗需求：</span>
						<el-input v-model="numberValidateForm.specialMedicalNeeds" type="textarea" :rows="3"
							style="width: calc(100% - 120px);" placeholder="请输入特殊医疗需求"></el-input>
					</div>
				</div>
			</div>
			<span slot="footer" class="dialog-footer" style="margin-top: 30px;text-align: center;">
				<el-button @click="dialogVisible = false">取 消</el-button>
				<el-button type="primary" @click="addNoticeTo()">确 定</el-button>
			</span>
		</el-dialog>
		<!--选择用户-->
		<el-dialog title="选择用户" :visible.sync="dialogVisibles" width="70%" center>
			<div style="margin:2% 0;display: inline-block;">
				<span>手机号:</span>
				<el-input style="width: 150px;" @keydown.enter.native="selectUser" clearable placeholder="请输入手机号"
					v-model="phoneUser"></el-input>
			</div>&emsp;&emsp;
			<div style="margin:2% 0;display: inline-block;">
				<span>昵称:</span>
				<el-input style="width: 150px;" @keydown.enter.native="selectUser" clearable placeholder="请输入昵称"
					v-model="userNameUser"></el-input>
			</div>&emsp;&emsp;
			<div style="display: inline-block;">
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="selectUser">查询
				</el-button>
				<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleansUser">重置
				</el-button>
			</div>
			<el-table v-loading="tableDataLoadings" :data="userList.list">
				<el-table-column fixed prop="userId" label="编号" width="80">
				</el-table-column>
				<el-table-column fixed prop="userName" label="昵称" width="150">
					<template slot-scope="scope">
						<span style="color: #f56c6c;">{{ scope.row.userName ? scope.row.userName : '未绑定' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="sex" label="性别">
					<template slot-scope="scope">
						<span v-if="scope.row.sex==1">男</span>
						<span v-else-if="scope.row.sex==2">女</span>
						<span v-else>未设置</span>
					</template>
				</el-table-column>
				<el-table-column label="图像">
					<template slot-scope="scope">
						<img v-if="scope.row.avatar==null" src="~@/assets/img/avatar.png" alt="" width="40" height="40">
						<img v-else :src="scope.row.avatar" alt="" width="40" height="40">
					</template>
				</el-table-column>
				<el-table-column prop="phone" label="手机号">
					<template slot-scope="scope">
						<!-- <span style="color: #4f9dec;cursor: pointer;" @click="updates(scope.row)">
							{{ scope.row.phone ? scope.row.phone : '未绑定' }}
						</span> -->
						<el-button size="mini" style="color: #008000;background: #fff;border: none;padding: 0;" type="primary"
							:disabled="!isAuth('userList:details')" @click="updates(scope.row.userId)">
							{{scope.row.phone ? scope.row.phone : '未绑定'}}
						</el-button>
					</template>
				</el-table-column>
				<el-table-column prop="invitationCode" label="邀请码"></el-table-column>
				<el-table-column label="邀请人邀请码">
					<template slot-scope="scope">
						<span style="color: #4f9dec;cursor: pointer;"
							@click="updates2(scope.row)">{{ scope.row.inviterCode ? scope.row.inviterCode : '未绑定' }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="80">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" :disabled="!isAuth('carBrandList:update')"
							@click="amendBanner(scope.row)" style="margin: 5px;">选择
						</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div style="text-align: center;margin-top: 10px;">
				<el-pagination @size-change="handleSizeChanges" @current-change="handleCurrentChanges"
					:page-sizes="[10, 20, 30, 40]" :page-size="limit1" :current-page="page1"
					layout="total,sizes, prev, pager, next,jumper" :total="userList.totalCount">
				</el-pagination>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				userList: {},
				gxList: [],
				// 新增字典数据
				maritalStatusList: [], // 婚姻状况
				insuranceTypeList: [], // 医保类型
				riskLevelList: [], // 风险等级
				page: 1,
				limit: 10,
				page1: 1,
				limit1: 10,
				tableData: {},
				memberName: '',
				memberImg: '',
				memberId: '',
				activeName: 'first',
				phone: '',
				userName: '',
				tableDataLoading: false,
				tableDataLoadings: false,
				dialogVisible: false,
				dialogVisibles: false,
				title: '添加就诊人',
				numberValidateForm: {
					realName: '',
					sex: 1,
					isUnderAge: 1,
					idNumber: '',
					relationship: '',
					userName: '',
					userId: '',
					phone: '',
					emergencyPhone: '',
					// 新增字段
					birthDate: '',
					ethnicity: '',
					maritalStatus: '',
					occupation: '',
					employer: '',
					currentAddress: '',
					registeredAddress: '',
					insuranceType: '',
					insuranceNumber: '',
					primaryContactName: '',
					primaryContactRelation: '',
					primaryContactPhone: '',
					secondaryContactName: '',
					secondaryContactRelation: '',
					secondaryContactPhone: '',
					majorMedicalHistory: '',
					drugAllergySummary: '',
					currentMedicationSummary: '',
					specialMedicalNeeds: '',
					riskLevel: 1,
				},
				titles: '添加就诊人',
				phoneUser: '',
				userNameUser: '',
				realNameT:'',
			}
		},
		methods: {
			handleClick(tab) {
				if (tab._props.label == '就诊人列表') {
					this.dataSelect()
				}
			},
			//获取用户列表
			getUserList() {
				this.tableDataLoadings = true
				this.$http({
					url: this.$http.adornUrl('user/selectUserList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.page1,
						'limit': this.limit1,
						'isPromotion': -1,
						'phone': this.phoneUser,
						'userName': this.userNameUser,
						'isAuthentication': 2,
						'isAgent': -1
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.tableDataLoadings = false
						let returnData = data.data;
						this.userList = returnData;
					}

				})
			},
			selectUser() {
				this.page1 = 1
				this.getUserList()
			},
			cleansUser() {
				this.page1 = 1
				this.phoneUser = ''
				this.userNameUser = ''
				this.getUserList()
			},
			// 用户列表弹框
			userBtn() {
				this.dialogVisibles = true;
				this.getUserList()
			},

			//获取就诊关系
			getgxList() {
				let data = {
					type: '就诊人关系'
				}
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams(data)
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.gxList = data.data
					}

				})
			},

			// 获取婚姻状况字典
			getMaritalStatusList() {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({
						'type': '婚姻状况'
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.maritalStatusList = data.data
					}
				})
			},

			// 获取医保类型字典
			getInsuranceTypeList() {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({
						'type': '医保类型'
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.insuranceTypeList = data.data
					}
				})
			},

			// 获取风险等级字典
			getRiskLevelList() {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({
						'type': '患者风险等级'
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.riskLevelList = data.data
					}
				})
			},
			// 获取数据列表
			dataSelect() {
				this.tableDataLoading = true
				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/getPatientList'),
					method: 'get',
					params: this.$http.adornParams({
						'page': this.page,
						'limit': this.limit,
						'realName': this.realNameT,
						'phone': this.phone,
						'userName':this.userName
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.tableDataLoading = false
						let returnData = data.data;
						this.tableData = returnData;
					}

				})
			},
			select() {
				this.page = 1
				this.dataSelect()
			},
			cleans() {
				this.page = 1
				this.userName = ''
				this.phone = ''
				this.realNameT = ''
				this.dataSelect()
			},
			handleCurrentChange(val) {
				this.page = val
				this.dataSelect()
			},
			handleSizeChange(val) {
				this.limit = val
				this.dataSelect()
			},
			handleCurrentChanges(val) {
				this.page1 = val
				this.getUserList()
			},
			handleSizeChanges(val) {
				this.limit1 = val
				this.getUserList()
			},
			// 添加、修改就诊人弹框
			addPatient(row) {
				if (row) {
					this.titles = '修改就诊人'
					this.numberValidateForm.userId = row.userId
					this.numberValidateForm.isUnderAge = row.isUnderAge
					this.numberValidateForm.sex = row.sex
					this.numberValidateForm.realName = row.realName
					this.numberValidateForm.phone = row.phone
					this.numberValidateForm.idNumber = row.idNumber
					this.numberValidateForm.emergencyPhone = row.emergencyPhone || ''
					this.numberValidateForm.relationship = row.relationship
					this.numberValidateForm.patientId = row.patientId
					this.numberValidateForm.userName = row.userName

					// 新增字段赋值
					this.numberValidateForm.birthDate = row.birthDate || ''
					this.numberValidateForm.ethnicity = row.ethnicity || ''
					this.numberValidateForm.maritalStatus = row.maritalStatus || ''
					this.numberValidateForm.occupation = row.occupation || ''
					this.numberValidateForm.employer = row.employer || ''
					this.numberValidateForm.currentAddress = row.currentAddress || ''
					this.numberValidateForm.registeredAddress = row.registeredAddress || ''
					this.numberValidateForm.insuranceType = row.insuranceType || ''
					this.numberValidateForm.insuranceNumber = row.insuranceNumber || ''
					this.numberValidateForm.primaryContactName = row.primaryContactName || ''
					this.numberValidateForm.primaryContactRelation = row.primaryContactRelation || ''
					this.numberValidateForm.primaryContactPhone = row.primaryContactPhone || ''
					this.numberValidateForm.secondaryContactName = row.secondaryContactName || ''
					this.numberValidateForm.secondaryContactRelation = row.secondaryContactRelation || ''
					this.numberValidateForm.secondaryContactPhone = row.secondaryContactPhone || ''
					this.numberValidateForm.majorMedicalHistory = row.majorMedicalHistory || ''
					this.numberValidateForm.drugAllergySummary = row.drugAllergySummary || ''
					this.numberValidateForm.currentMedicationSummary = row.currentMedicationSummary || ''
					this.numberValidateForm.specialMedicalNeeds = row.specialMedicalNeeds || ''
					this.numberValidateForm.riskLevel = row.riskLevel || 1
				} else {
					this.titles = '添加就诊人'
					// 重置所有字段
					this.numberValidateForm = {
						userId: '',
						isUnderAge: 1,
						sex: 1,
						realName: '',
						phone: '',
						idNumber: '',
						relationship: '',
						patientId: '',
						userName: '',
						emergencyPhone: '',
						birthDate: '',
						ethnicity: '',
						maritalStatus: '',
						occupation: '',
						employer: '',
						currentAddress: '',
						registeredAddress: '',
						insuranceType: '',
						insuranceNumber: '',
						primaryContactName: '',
						primaryContactRelation: '',
						primaryContactPhone: '',
						secondaryContactName: '',
						secondaryContactRelation: '',
						secondaryContactPhone: '',
						majorMedicalHistory: '',
						drugAllergySummary: '',
						currentMedicationSummary: '',
						specialMedicalNeeds: '',
						riskLevel: 1,
					}
				}
				this.dialogVisible = true
			},
			// 添加/修改就诊人
			addNoticeTo() {
				if (this.numberValidateForm.userId == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请选择所属用户',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.realName == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入姓名',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.idNumber == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入身份证号码',
						type: 'warning'
					});
					return
				}
				if (this.numberValidateForm.phone == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请输入手机号',
						type: 'warning'
					});
					return
				}
				// if (this.numberValidateForm.emergencyPhone == '') {
				// 	this.$notify({
				// 		title: '提示',
				// 		duration: 1800,
				// 		message: '请输入紧急联系人电话',
				// 		type: 'warning'
				// 	});
				// 	return
				// }
				if (this.numberValidateForm.relationship == '') {
					this.$notify({
						title: '提示',
						duration: 1800,
						message: '请选择就诊人关系',
						type: 'warning'
					});
					return
				}
				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/savePatient'),
					method: 'post',
					// data: this.$http.adornData({
					params: this.$http.adornParams({
						'userId': this.numberValidateForm.userId,
						'isUnderAge': this.numberValidateForm.isUnderAge,
						'sex': this.numberValidateForm.sex,
						'realName': this.numberValidateForm.realName,
						'phone': this.numberValidateForm.phone,
						'idNumber': this.numberValidateForm.idNumber,
						'relationship': this.numberValidateForm.relationship,
						'patientId': this.numberValidateForm.patientId,
						'emergencyPhone': this.numberValidateForm.emergencyPhone,
						// 新增字段
						'birthDate': this.numberValidateForm.birthDate,
						'ethnicity': this.numberValidateForm.ethnicity,
						'maritalStatus': this.numberValidateForm.maritalStatus,
						'occupation': this.numberValidateForm.occupation,
						'employer': this.numberValidateForm.employer,
						'currentAddress': this.numberValidateForm.currentAddress,
						'registeredAddress': this.numberValidateForm.registeredAddress,
						'insuranceType': this.numberValidateForm.insuranceType,
						'insuranceNumber': this.numberValidateForm.insuranceNumber,
						'primaryContactName': this.numberValidateForm.primaryContactName,
						'primaryContactRelation': this.numberValidateForm.primaryContactRelation,
						'primaryContactPhone': this.numberValidateForm.primaryContactPhone,
						'secondaryContactName': this.numberValidateForm.secondaryContactName,
						'secondaryContactRelation': this.numberValidateForm.secondaryContactRelation,
						'secondaryContactPhone': this.numberValidateForm.secondaryContactPhone,
						'majorMedicalHistory': this.numberValidateForm.majorMedicalHistory,
						'drugAllergySummary': this.numberValidateForm.drugAllergySummary,
						'currentMedicationSummary': this.numberValidateForm.currentMedicationSummary,
						'specialMedicalNeeds': this.numberValidateForm.specialMedicalNeeds,
						'riskLevel': this.numberValidateForm.riskLevel,
					})
				}).then(({
					data
				}) => {
					if (data.code == 0) {
						this.dialogVisible = false
						this.$message({
							message: '操作成功',
							type: 'success',
							duration: 1500,
							onClose: () => {
								this.dataSelect()
							}
						})
					} else {
						this.$message({
							message: data.msg,
							type: 'warning',
							duration: 1500,
							onClose: () => {}
						})
					}

				})
			},
			// 选择用户
			amendBanner(row) {
				this.numberValidateForm.userName = row.userName
				this.numberValidateForm.userId = row.userId
				this.dialogVisibles = false
			},
			//删除
			deletes(row) {
				this.$confirm(`确定删除此条信息?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					let data = {
						userId: row.userId,
						patientId: row.patientId
					}
					this.$http({
						url: this.$http.adornUrl('admin/patientInfo/deletePatient'),
						method: 'get',
						params: this.$http.adornParams(data)
					}).then(({
						data
					}) => {
						if (data.code == 0) {
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						} else {
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500,
								onClose: () => {}
							})
						}

					})
				}).catch(() => {})
			},
			// 详情跳转
			updates(userId) {
				if (userId) {
					this.$router.push({
						path: '/userDetail',
						query: {
							userId: userId
						}
					})
				}

			},

			// 获取婚姻状况文本
			getMaritalStatusText(code) {
				if (!code) return ''
				const item = this.maritalStatusList.find(item => item.code == code)
				return item ? item.value : ''
			},

			// 获取医保类型文本
			getInsuranceTypeText(code) {
				if (!code) return ''
				const item = this.insuranceTypeList.find(item => item.code == code)
				return item ? item.value : ''
			},

			// 获取风险等级文本
			getRiskLevelText(code) {
				if (!code) return ''
				const item = this.riskLevelList.find(item => item.code == code)
				return item ? item.value : ''
			},

			// 查看健康记录
			viewHealthRecords(row) {
				this.$router.push({
					path: '/healthRecordList',
					query: {
						patientId: row.patientId,
						patientName: row.realName,
						userId: row.userId
					}
				})
			},
		},
		mounted() {
			this.dataSelect()
			this.getgxList();
			// 获取字典数据
			this.getMaritalStatusList();
			this.getInsuranceTypeList();
			this.getRiskLevelList();
		}
	}
</script>

<style>
</style>
