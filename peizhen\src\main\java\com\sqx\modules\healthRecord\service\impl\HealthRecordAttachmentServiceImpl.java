package com.sqx.modules.healthRecord.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.dao.HealthRecordAttachmentDao;
import com.sqx.modules.healthRecord.entity.HealthRecordAttachment;
import com.sqx.modules.healthRecord.service.HealthRecordAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 健康记录附件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Service
public class HealthRecordAttachmentServiceImpl extends ServiceImpl<HealthRecordAttachmentDao, HealthRecordAttachment> implements HealthRecordAttachmentService {

    @Autowired
    private HealthRecordAttachmentDao attachmentDao;

    @Value("${file.upload.path:/uploads/health-records/}")
    private String uploadPath;

    @Value("${file.access.url:http://localhost:8080/uploads/health-records/}")
    private String accessUrl;

    @Override
    public Result uploadAttachment(Long recordId, MultipartFile file, Integer attachmentType, String description, Long uploadedBy) {
        if (file.isEmpty()) {
            return Result.error("上传文件不能为空");
        }

        try {
            // 获取原始文件名和扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }

            // 生成新的文件名
            String fileName = UUID.randomUUID().toString() + fileExtension;
            
            // 按日期创建目录
            String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fullUploadPath = uploadPath + dateDir + "/";
            
            // 创建目录
            File uploadDir = new File(fullUploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 保存文件
            String filePath = fullUploadPath + fileName;
            File destFile = new File(filePath);
            file.transferTo(destFile);

            // 构建访问URL
            String fileUrl = accessUrl + dateDir + "/" + fileName;

            // 保存附件信息到数据库
            HealthRecordAttachment attachment = new HealthRecordAttachment();
            attachment.setRecordId(recordId);
            attachment.setFileName(fileName);
            attachment.setFileOriginalName(originalFilename);
            attachment.setFilePath(filePath);
            attachment.setFileUrl(fileUrl);
            attachment.setFileSize(file.getSize());
            attachment.setFileType(getFileType(file.getContentType()));
            attachment.setFileExtension(fileExtension);
            attachment.setMimeType(file.getContentType());
            attachment.setAttachmentType(attachmentType);
            attachment.setDescription(description);
            attachment.setUploadTime(LocalDateTime.now());
            attachment.setUploadedBy(uploadedBy);
            attachment.setIsDelete(0);
            attachment.setCreateTime(LocalDateTime.now());
            attachment.setUpdateTime(LocalDateTime.now());

            attachmentDao.insert(attachment);

            return Result.success("文件上传成功").put("data", attachment);

        } catch (IOException e) {
            return Result.error("文件上传失败：" + e.getMessage());
        }
    }

    @Override
    public List<HealthRecordAttachment> getAttachmentsByRecordId(Long recordId) {
        return attachmentDao.getAttachmentsByRecordId(recordId);
    }

    @Override
    public Result deleteAttachment(Long attachmentId, Long operatorId) {
        HealthRecordAttachment attachment = attachmentDao.selectById(attachmentId);
        if (attachment == null) {
            return Result.error("附件不存在");
        }

        attachment.setIsDelete(1);
        attachment.setUpdateTime(LocalDateTime.now());
        attachmentDao.updateById(attachment);

        // 可以选择是否删除物理文件
        // deletePhysicalFile(attachment.getFilePath());

        return Result.success("附件删除成功");
    }

    @Override
    public Result deleteAttachmentsByRecordId(Long recordId, Long operatorId) {
        int count = attachmentDao.deleteAttachmentsByRecordId(recordId, operatorId);
        return Result.success("删除了 " + count + " 个附件");
    }

    @Override
    public HealthRecordAttachment getAttachmentForDownload(Long attachmentId) {
        return attachmentDao.selectById(attachmentId);
    }

    /**
     * 根据MIME类型判断文件类型
     */
    private String getFileType(String mimeType) {
        if (mimeType == null) {
            return "other";
        }
        
        if (mimeType.startsWith("image/")) {
            return "image";
        } else if (mimeType.startsWith("video/")) {
            return "video";
        } else if (mimeType.startsWith("audio/")) {
            return "audio";
        } else if (mimeType.contains("pdf") || mimeType.contains("document") || 
                   mimeType.contains("text") || mimeType.contains("sheet")) {
            return "document";
        } else {
            return "other";
        }
    }

    /**
     * 删除物理文件
     */
    private void deletePhysicalFile(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            // 记录日志，但不影响业务流程
            log.warn("删除物理文件失败：" + filePath, e);
        }
    }
}
