package com.sqx.modules.healthRecord.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sqx.modules.healthRecord.entity.HealthStatusRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 健康状况记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Mapper
public interface HealthStatusRecordDao extends BaseMapper<HealthStatusRecord> {

    /**
     * 分页查询健康记录列表
     * @param pages 分页参数
     * @param healthRecord 查询条件
     * @return 分页结果
     */
    IPage<HealthStatusRecord> getHealthRecordList(@Param("pages") Page<HealthStatusRecord> pages, 
                                                  @Param("healthRecord") HealthStatusRecord healthRecord);

    /**
     * 根据患者ID查询健康记录列表
     * @param pages 分页参数
     * @param patientId 患者ID
     * @return 分页结果
     */
    IPage<HealthStatusRecord> getHealthRecordsByPatientId(@Param("pages") Page<HealthStatusRecord> pages, 
                                                          @Param("patientId") Long patientId);

    /**
     * 根据用户ID查询健康记录列表
     * @param pages 分页参数
     * @param userId 用户ID
     * @return 分页结果
     */
    IPage<HealthStatusRecord> getHealthRecordsByUserId(@Param("pages") Page<HealthStatusRecord> pages, 
                                                       @Param("userId") Long userId);
}
