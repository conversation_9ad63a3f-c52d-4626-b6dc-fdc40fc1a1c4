package com.sqx.modules.healthRecord.controller;

import com.sqx.common.utils.Result;
import com.sqx.modules.healthRecord.entity.HealthStatusRecord;
import com.sqx.modules.healthRecord.service.HealthStatusRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 健康状况记录表-管理端 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@RestController
@Api(value = "健康记录管理-管理端", tags = {"健康记录管理-管理端"})
@RequestMapping("/admin/healthRecord/")
public class AdminHealthStatusRecordController {

    @Autowired
    private HealthStatusRecordService healthStatusRecordService;

    @ApiOperation("添加或修改健康记录")
    @PostMapping("saveHealthRecord")
    public Result saveHealthRecord(HealthStatusRecord healthRecord) {
        return healthStatusRecordService.saveHealthRecord(healthRecord);
    }

    @ApiOperation("获取健康记录列表")
    @GetMapping("getHealthRecordList")
    public Result getHealthRecordList(
            @ApiParam("页码") Integer page,
            @ApiParam("每页数量") Integer limit,
            HealthStatusRecord healthRecord) {
        return Result.success().put("data", healthStatusRecordService.getHealthRecordList(page, limit, healthRecord));
    }

    @ApiOperation("获取健康记录详情")
    @GetMapping("getHealthRecordInfo")
    public Result getHealthRecordInfo(@ApiParam("记录ID") Long recordId) {
        return Result.success().put("data", healthStatusRecordService.getHealthRecordInfo(recordId));
    }

    @ApiOperation("根据患者ID获取健康记录列表")
    @GetMapping("getHealthRecordsByPatientId")
    public Result getHealthRecordsByPatientId(
            @ApiParam("页码") Integer page,
            @ApiParam("每页数量") Integer limit,
            @ApiParam("患者ID") Long patientId) {
        return Result.success().put("data", healthStatusRecordService.getHealthRecordsByPatientId(page, limit, patientId));
    }

    @ApiOperation("根据用户ID获取健康记录列表")
    @GetMapping("getHealthRecordsByUserId")
    public Result getHealthRecordsByUserId(
            @ApiParam("页码") Integer page,
            @ApiParam("每页数量") Integer limit,
            @ApiParam("用户ID") Long userId) {
        return Result.success().put("data", healthStatusRecordService.getHealthRecordsByUserId(page, limit, userId));
    }

    @ApiOperation("删除健康记录")
    @GetMapping("deleteHealthRecord")
    public Result deleteHealthRecord(
            @ApiParam("记录ID") Long recordId,
            @ApiParam("操作人ID") Long operatorId) {
        return healthStatusRecordService.deleteHealthRecord(recordId, operatorId);
    }

    @ApiOperation("批量删除健康记录")
    @PostMapping("batchDeleteHealthRecords")
    public Result batchDeleteHealthRecords(
            @ApiParam("记录ID数组") @RequestParam Long[] recordIds,
            @ApiParam("操作人ID") Long operatorId) {
        return healthStatusRecordService.batchDeleteHealthRecords(recordIds, operatorId);
    }
}
