<template>
	<div>
		<el-card class="box-card">
			<div slot="header" class="clearfix">
				<span>{{ title }}</span>
				<el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
			</div>

			<el-form :model="healthRecordForm" :rules="rules" ref="healthRecordForm" label-width="120px">
				<!-- 基本信息 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">基本信息</h4>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">患者：</span>
							<el-input v-model="healthRecordForm.patientName" @focus="selectPatient()" style="width: 200px;" placeholder="请选择患者" readonly>
								<el-button slot="append" icon="el-icon-search" @click="selectPatient()">选择</el-button>
							</el-input>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">记录日期：</span>
							<el-date-picker v-model="healthRecordForm.recordDate" type="date" placeholder="请选择记录日期"
								style="width: 200px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">记录时间：</span>
							<el-time-picker v-model="healthRecordForm.recordTime" placeholder="请选择记录时间"
								style="width: 200px;" format="HH:mm:ss" value-format="HH:mm:ss"></el-time-picker>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">记录类型：</span>
							<el-select v-model="healthRecordForm.recordType" placeholder="请选择记录类型" style="width: 200px;">
								<el-option v-for="(item,index) in recordTypeList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">健康状态：</span>
							<el-select v-model="healthRecordForm.healthStatus" placeholder="请选择健康状态" style="width: 200px;">
								<el-option label="优秀" :value="1"></el-option>
								<el-option label="良好" :value="2"></el-option>
								<el-option label="一般" :value="3"></el-option>
								<el-option label="较差" :value="4"></el-option>
								<el-option label="差" :value="5"></el-option>
							</el-select>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">风险评估：</span>
							<el-select v-model="healthRecordForm.riskAssessment" placeholder="请选择风险评估" style="width: 200px;">
								<el-option label="低风险" :value="1"></el-option>
								<el-option label="中风险" :value="2"></el-option>
								<el-option label="高风险" :value="3"></el-option>
							</el-select>
						</div>
					</div>
				</div>

				<!-- 生理指标 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">生理指标</h4>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">体重(kg)：</span>
							<el-input-number v-model="healthRecordForm.weight" :precision="2" :step="0.1" :min="0" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">身高(cm)：</span>
							<el-input-number v-model="healthRecordForm.height" :precision="2" :step="0.1" :min="0" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">BMI：</span>
							<el-input v-model="healthRecordForm.bmi" style="width: 200px;" readonly placeholder="自动计算"></el-input>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">收缩压：</span>
							<el-input-number v-model="healthRecordForm.bloodPressureSystolic" :min="0" :max="300" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">舒张压：</span>
							<el-input-number v-model="healthRecordForm.bloodPressureDiastolic" :min="0" :max="200" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">心率(次/分)：</span>
							<el-input-number v-model="healthRecordForm.heartRate" :min="0" :max="300" style="width: 200px;"></el-input-number>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">体温(℃)：</span>
							<el-input-number v-model="healthRecordForm.bodyTemperature" :precision="1" :step="0.1" :min="30" :max="45" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">血糖(mmol/L)：</span>
							<el-input-number v-model="healthRecordForm.bloodSugar" :precision="1" :step="0.1" :min="0" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">疼痛等级：</span>
							<el-select v-model="healthRecordForm.painLevel" placeholder="请选择疼痛等级" style="width: 200px;">
								<el-option v-for="i in 11" :key="i-1" :label="i-1 + '级'" :value="i-1"></el-option>
							</el-select>
						</div>
					</div>
				</div>

				<!-- 生活方式 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">生活方式</h4>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">运动时长(分钟)：</span>
							<el-input-number v-model="healthRecordForm.exerciseDuration" :min="0" style="width: 200px;"></el-input-number>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">运动类型：</span>
							<el-input v-model="healthRecordForm.exerciseType" style="width: 200px;" placeholder="如：跑步、游泳等"></el-input>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">睡眠时长(小时)：</span>
							<el-input-number v-model="healthRecordForm.sleepHours" :precision="1" :step="0.5" :min="0" :max="24" style="width: 200px;"></el-input-number>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 15px; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">睡眠质量：</span>
							<el-select v-model="healthRecordForm.sleepQuality" placeholder="请选择睡眠质量" style="width: 200px;">
								<el-option v-for="(item,index) in sleepQualityList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">情绪状态：</span>
							<el-select v-model="healthRecordForm.moodStatus" placeholder="请选择情绪状态" style="width: 200px;">
								<el-option v-for="(item,index) in moodStatusList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">用药依从性：</span>
							<el-select v-model="healthRecordForm.medicationAdherence" placeholder="请选择用药依从性" style="width: 200px;">
								<el-option v-for="(item,index) in medicationAdherenceList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>
						</div>
					</div>
				</div>
				<!-- 详细信息 -->
				<div style="border: 1px solid #e6e6e6; padding: 15px; margin-bottom: 15px; border-radius: 4px;">
					<h4 style="margin: 0 0 15px 0; color: #409EFF; border-bottom: 1px solid #e6e6e6; padding-bottom: 8px;">详细信息</h4>

					<div style="display: flex; margin-bottom: 15px; align-items: flex-start;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">症状描述：</span>
							<el-input v-model="healthRecordForm.symptoms" type="textarea" :rows="3"
								style="width: calc(100% - 120px);" placeholder="请输入症状描述"></el-input>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">疼痛部位：</span>
							<el-input v-model="healthRecordForm.painLocation" type="textarea" :rows="3"
								style="width: calc(100% - 120px);" placeholder="请输入疼痛部位"></el-input>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 15px; align-items: flex-start;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">用药情况：</span>
							<el-input v-model="healthRecordForm.medicationTaken" type="textarea" :rows="3"
								style="width: calc(100% - 120px);" placeholder="请输入当日用药情况"></el-input>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">饮食记录：</span>
							<el-input v-model="healthRecordForm.dietNotes" type="textarea" :rows="3"
								style="width: calc(100% - 120px);" placeholder="请输入饮食记录"></el-input>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 15px; align-items: flex-start;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">特殊事件：</span>
							<el-input v-model="healthRecordForm.specialEvents" type="textarea" :rows="3"
								style="width: calc(100% - 120px);" placeholder="请输入特殊事件记录"></el-input>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px; vertical-align: top; line-height: 32px;">医生建议：</span>
							<el-input v-model="healthRecordForm.doctorAdvice" type="textarea" :rows="3"
								style="width: calc(100% - 120px);" placeholder="请输入医生建议"></el-input>
						</div>
					</div>

					<div style="display: flex; margin-bottom: 0; align-items: center;">
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">下次随访日期：</span>
							<el-date-picker v-model="healthRecordForm.nextFollowupDate" type="date" placeholder="请选择下次随访日期"
								style="width: 200px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
						</div>
						<div style="flex: 1; margin-right: 20px;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">是否异常：</span>
							<el-radio-group v-model="healthRecordForm.isAbnormal">
								<el-radio :label="0">正常</el-radio>
								<el-radio :label="1">异常</el-radio>
							</el-radio-group>
						</div>
						<div style="flex: 1;">
							<span style="width: 100px; display: inline-block; text-align: right; margin-right: 10px;">数据来源：</span>
							<el-select v-model="healthRecordForm.dataSource" placeholder="请选择数据来源" style="width: 200px;">
								<el-option v-for="(item,index) in dataSourceList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>
						</div>
					</div>
				</div>

				<div style="text-align: center; margin-top: 30px;">
					<el-button @click="goBack">取 消</el-button>
					<el-button type="primary" @click="saveHealthRecord">确 定</el-button>
				</div>
			</el-form>
		</el-card>

		<!-- 患者选择对话框 -->
		<el-dialog title="选择患者" :visible.sync="dialogVisiblePatient" width="80%" :close-on-click-modal="false">
			<div style="margin-bottom: 15px;">
				<el-input v-model="patientSearchName" placeholder="请输入患者姓名搜索" style="width: 300px;" @keydown.enter.native="searchPatients"></el-input>
				<el-button type="primary" @click="searchPatients" style="margin-left: 10px;">搜索</el-button>
			</div>

			<el-table :data="patientList" v-loading="patientLoading" @row-click="selectPatientRow" style="cursor: pointer;">
				<el-table-column prop="patientId" label="患者ID" width="80"></el-table-column>
				<el-table-column prop="realName" label="患者姓名" width="120"></el-table-column>
				<el-table-column prop="phone" label="手机号" width="130"></el-table-column>
				<el-table-column prop="sex" label="性别" width="80">
					<template slot-scope="scope">
						<span>{{ scope.row.sex === 1 ? '男' : scope.row.sex === 2 ? '女' : '未知' }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="age" label="年龄" width="80"></el-table-column>
				<el-table-column prop="idNumber" label="身份证号" width="180"></el-table-column>
				<el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
				<el-table-column label="操作" width="100">
					<template slot-scope="scope">
						<el-button size="mini" type="primary" @click="confirmSelectPatient(scope.row)">选择</el-button>
					</template>
				</el-table-column>
			</el-table>

			<div style="text-align: center; margin-top: 15px;">
				<el-pagination @current-change="handlePatientPageChange" :current-page="patientPage"
					:page-size="patientLimit" layout="prev, pager, next" :total="patientTotal">
				</el-pagination>
			</div>

			<div slot="footer" class="dialog-footer">
				<el-button @click="dialogVisiblePatient = false">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				title: '添加健康记录',
				healthRecordForm: {
					recordId: '',
					patientId: '',
					patientName: '',
					userId: '',
					recordDate: '',
					recordTime: '',
					recordType: 1,
					healthStatus: 2,
					symptoms: '',
					weight: null,
					height: null,
					bmi: '',
					bloodPressureSystolic: null,
					bloodPressureDiastolic: null,
					heartRate: null,
					bodyTemperature: null,
					bloodSugar: null,
					medicationTaken: '',
					medicationAdherence: null,
					exerciseDuration: null,
					exerciseType: '',
					sleepHours: null,
					sleepQuality: null,
					moodStatus: null,
					painLevel: 0,
					painLocation: '',
					dietNotes: '',
					specialEvents: '',
					doctorAdvice: '',
					nextFollowupDate: '',
					riskAssessment: 1,
					dataSource: 1,
					isAbnormal: 0
				},
				rules: {
					patientId: [
						{ required: true, message: '请选择患者', trigger: 'change' }
					],
					recordDate: [
						{ required: true, message: '请选择记录日期', trigger: 'change' }
					],
					recordType: [
						{ required: true, message: '请选择记录类型', trigger: 'change' }
					],
					healthStatus: [
						{ required: true, message: '请选择健康状态', trigger: 'change' }
					]
				},
				recordTypeList: [],
				sleepQualityList: [],
				moodStatusList: [],
				medicationAdherenceList: [],
				dataSourceList: [],
				patientList: [],
				dialogVisiblePatient: false,
				patientLoading: false,
				patientSearchName: '',
				patientPage: 1,
				patientLimit: 10,
				patientTotal: 0
			}
		},
		methods: {
			// 获取字典数据
			getDictData() {
				// 获取记录类型
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '记录类型' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.recordTypeList = data.data
					}
				})

				// 获取睡眠质量
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '睡眠质量' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.sleepQualityList = data.data
					}
				})

				// 获取情绪状态
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '情绪状态' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.moodStatusList = data.data
					}
				})

				// 获取用药依从性
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '用药依从性' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.medicationAdherenceList = data.data
					}
				})

				// 获取数据来源
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({ 'type': '数据来源' })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.dataSourceList = data.data
					}
				})
			},

			// 选择患者
			selectPatient() {
				this.dialogVisiblePatient = true
				this.patientPage = 1
				this.patientSearchName = ''
				this.searchPatients()
			},

			// 搜索患者
			searchPatients() {
				this.patientLoading = true
				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/getPatientList'),
					method: 'get',
					params: this.$http.adornParams({
						page: this.patientPage,
						limit: this.patientLimit,
						realName: this.patientSearchName
					})
				}).then(({ data }) => {
					this.patientLoading = false
					if (data && data.code === 0) {
						this.patientList = data.data.records || []
						this.patientTotal = data.data.total || 0
					}
				}).catch(() => {
					this.patientLoading = false
				})
			},

			// 患者分页
			handlePatientPageChange(page) {
				this.patientPage = page
				this.searchPatients()
			},

			// 选择患者行
			selectPatientRow(row) {
				// 可以通过点击行来选择
				this.confirmSelectPatient(row)
			},

			// 确认选择患者
			confirmSelectPatient(patient) {
				this.healthRecordForm.patientId = patient.patientId
				this.healthRecordForm.patientName = patient.realName
				this.healthRecordForm.userId = patient.userId
				this.dialogVisiblePatient = false
				this.$message.success('患者选择成功')
			},

			// 保存健康记录
			saveHealthRecord() {
				this.$refs.healthRecordForm.validate((valid) => {
					if (valid) {
						this.$http({
							url: this.$http.adornUrl('admin/healthRecord/saveHealthRecord'),
							method: 'post',
							data: this.$http.adornData(this.healthRecordForm)
						}).then(({ data }) => {
							if (data.code == 0) {
								this.$message({
									message: '操作成功',
									type: 'success',
									duration: 1500,
									onClose: () => {
										this.goBack()
									}
								})
							} else {
								this.$message({
									message: data.msg,
									type: 'warning',
									duration: 1500
								})
							}
						})
					}
				})
			},

			// 返回列表
			goBack() {
				this.$router.go(-1)
			},

			// 获取健康记录详情
			getHealthRecordInfo(recordId) {
				this.$http({
					url: this.$http.adornUrl('admin/healthRecord/getHealthRecordInfo'),
					method: 'get',
					params: this.$http.adornParams({ recordId: recordId })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						this.healthRecordForm = { ...this.healthRecordForm, ...data.data }
					}
				})
			},

			// 根据患者ID获取患者信息
			getPatientInfo(patientId) {
				this.$http({
					url: this.$http.adornUrl('admin/patientInfo/getPatientInfo'),
					method: 'get',
					params: this.$http.adornParams({ patientId: patientId })
				}).then(({ data }) => {
					if (data && data.code === 0) {
						const patient = data.data
						this.healthRecordForm.patientId = patient.patientId
						this.healthRecordForm.patientName = patient.realName
						this.healthRecordForm.userId = patient.userId
					}
				}).catch(() => {
					this.$message.error('获取患者信息失败')
				})
			},

			// 计算BMI
			calculateBMI() {
				if (this.healthRecordForm.weight && this.healthRecordForm.height &&
					this.healthRecordForm.weight > 0 && this.healthRecordForm.height > 0) {
					const heightInMeters = this.healthRecordForm.height / 100
					const bmi = this.healthRecordForm.weight / (heightInMeters * heightInMeters)
					this.healthRecordForm.bmi = bmi.toFixed(2)
				} else {
					this.healthRecordForm.bmi = ''
				}
			}
		},

		watch: {
			// 监听体重和身高变化，自动计算BMI
			'healthRecordForm.weight': function() {
				this.calculateBMI()
			},
			'healthRecordForm.height': function() {
				this.calculateBMI()
			}
		},

		mounted() {
			// 获取路由参数
			const recordId = this.$route.query.recordId
			const patientId = this.$route.query.patientId
			const patientName = this.$route.query.patientName
			const userId = this.$route.query.userId

			if (recordId) {
				this.title = '修改健康记录'
				this.getHealthRecordInfo(recordId)
			} else {
				this.title = '添加健康记录'
				if (patientId) {
					// 如果有患者ID，根据ID获取患者详细信息
					this.getPatientInfo(patientId)
				} else if (patientName && userId) {
					// 如果有患者姓名和用户ID，直接使用
					this.healthRecordForm.patientName = patientName
					this.healthRecordForm.userId = userId
				}
				// 设置默认日期和时间为当前时间
				const now = new Date()
				this.healthRecordForm.recordDate = now.toISOString().split('T')[0]
				this.healthRecordForm.recordTime = now.toTimeString().split(' ')[0]
			}

			this.getDictData()
		}
	}
</script>

<style>
</style>
