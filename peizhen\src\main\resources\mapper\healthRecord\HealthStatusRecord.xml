<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sqx.modules.healthRecord.dao.HealthStatusRecordDao">
    
    <!-- 分页查询健康记录列表 -->
    <select id="getHealthRecordList" resultType="com.sqx.modules.healthRecord.entity.HealthStatusRecord">
        SELECT 
            h.*,
            p.real_name as patientName,
            u.user_name as userName
        FROM health_status_record h
        LEFT JOIN patient_info p ON h.patient_id = p.patient_id
        LEFT JOIN tb_user u ON h.user_id = u.user_id
        WHERE h.is_delete = 0
        <if test="healthRecord.patientId != null">
            AND h.patient_id = #{healthRecord.patientId}
        </if>
        <if test="healthRecord.userId != null">
            AND h.user_id = #{healthRecord.userId}
        </if>
        <if test="healthRecord.recordType != null">
            AND h.record_type = #{healthRecord.recordType}
        </if>
        <if test="healthRecord.healthStatus != null">
            AND h.health_status = #{healthRecord.healthStatus}
        </if>
        <if test="healthRecord.recordDate != null">
            AND h.record_date = #{healthRecord.recordDate}
        </if>
        <if test="healthRecord.isAbnormal != null">
            AND h.is_abnormal = #{healthRecord.isAbnormal}
        </if>
        <if test="healthRecord.riskAssessment != null">
            AND h.risk_assessment = #{healthRecord.riskAssessment}
        </if>
        <if test="healthRecord.patientName != null and healthRecord.patientName != ''">
            AND p.real_name LIKE CONCAT('%', #{healthRecord.patientName}, '%')
        </if>
        <if test="healthRecord.userName != null and healthRecord.userName != ''">
            AND u.user_name LIKE CONCAT('%', #{healthRecord.userName}, '%')
        </if>
        ORDER BY h.record_date DESC, h.record_time DESC, h.create_time DESC
    </select>

    <!-- 根据患者ID查询健康记录列表 -->
    <select id="getHealthRecordsByPatientId" resultType="com.sqx.modules.healthRecord.entity.HealthStatusRecord">
        SELECT 
            h.*,
            p.real_name as patientName,
            u.user_name as userName
        FROM health_status_record h
        LEFT JOIN patient_info p ON h.patient_id = p.patient_id
        LEFT JOIN tb_user u ON h.user_id = u.user_id
        WHERE h.is_delete = 0 AND h.patient_id = #{patientId}
        ORDER BY h.record_date DESC, h.record_time DESC, h.create_time DESC
    </select>

    <!-- 根据用户ID查询健康记录列表 -->
    <select id="getHealthRecordsByUserId" resultType="com.sqx.modules.healthRecord.entity.HealthStatusRecord">
        SELECT 
            h.*,
            p.real_name as patientName,
            u.user_name as userName
        FROM health_status_record h
        LEFT JOIN patient_info p ON h.patient_id = p.patient_id
        LEFT JOIN tb_user u ON h.user_id = u.user_id
        WHERE h.is_delete = 0 AND h.user_id = #{userId}
        ORDER BY h.record_date DESC, h.record_time DESC, h.create_time DESC
    </select>

</mapper>
