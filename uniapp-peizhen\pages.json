{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationBarBackgroundColor": "#0175FE",
				"enablePullDownRefresh": true,
				"navigationBarTextStyle": "white",
				"navigationStyle": "custom" // 隐藏系统导航栏
			}
		},
		{
			"path": "pages/index/search/index",
			"style": {
				"navigationBarTitleText": "搜索",
				// #ifdef h5
				"navigationStyle": "custom",
				// #endif 
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/index/game/gameList",
			"style": {
				"navigationBarTitleText": "护理员",
				"enablePullDownRefresh": true

			}
		},
		{
			"path": "pages/index/game/orderDet",
			"style": {
				"navigationBarTitleText": "填写订单",
				"enablePullDownRefresh": true
			}
		},

		{
			"path": "pages/index/citys/citys",
			"style": {
				"navigationBarTitleText": "城市"
			}
		},
		{
			"path": "pages/index/webView",
			"style": {
				"navigationBarTitleText": "网页"
			}
		},

		{
			"path": "pages/msg/index",
			"style": {
				"navigationBarTitleText": "消息"
			}
		},
		{
			"path": "pages/msg/im",
			"style": {
				"navigationBarTitleText": "消息"
			}
		},
		{
			"path": "pages/msg/message",
			"style": {
				"navigationBarTitleText": "系统消息",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/ai/index",
			"style": {
				"navigationBarTitleText": "AI助手",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/ai/chat",
			"style": {
				"navigationBarTitleText": "AI对话",
				"navigationBarBackgroundColor": "#0175FE",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/ai/models",
			"style": {
				"navigationBarTitleText": "选择AI模型"
			}
		},

		{
			"path": "pages/my/index",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationBarBackgroundColor": "#e1edff",
				// #ifdef h5
				"navigationStyle": "custom",
				// #endif 
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/my/invitationUser",
			"style": {
				"navigationBarTitleText": "分享好友",
				"enablePullDownRefresh": false,
				"app-plus": {
					"bounce": "none",
					"scrollIndicator": "none",
					"titleNView": true
				}
			}
		},
		{
			"path": "pages/my/userinfo",
			"style": {
				"navigationBarTitleText": "个人信息"
			}
		},

		{
			"path": "pages/public/login",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/public/bind",
			"style": {
				"navigationBarTitleText": "绑定手机号"
			}
		},
		{
			"path": "pages/public/forgetPwd",
			"style": {
				"navigationBarTitleText": "重置密码"
			}
		},

		{
			"path": "pages/public/loginphone",
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/public/pwd",
			"style": {
				"navigationBarTitleText": "修改密码"
			}
		},
		{
			"path": "pages/public/register",
			"style": {
				"navigationBarTitleText": "注册"
			}
		}, {
			"path": "pages/order/index",
			"style": {
				"navigationBarTitleText": "我的订单",
				// #ifdef h5
				"navigationStyle": "custom",
				// #endif 
				"app-plus": {
					"titleNView": false
				}
			}
		}, {
			"path": "pages/webView/webView",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/kefu/kefu",
			"style": {
				"navigationBarTitleText": "客服",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/peizhenPeop/peizhenPeop",
			"style": {
				"navigationBarTitleText": "陪诊师",
				"enablePullDownRefresh": false
			}

		}
	],
	"subPackages": [{
		"root": "my",
		"pages": [{
				"path": "keshi/index",
				"style": {
					"navigationBarTitleText": "全部科室"
				}
			}, {
				"path": "youhuijuan/index",
				"style": {
					"navigationBarTitleText": "优惠券"
					// "enablePullDownRefresh": true
				}
			}, {
				"path": "youhuijuan/myList",
				"style": {
					"navigationBarTitleText": "我的优惠券",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "team/earnings",
				"style": {
					"navigationBarTitleText": "我的收益",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "team/team",
				"style": {
					"navigationBarTitleText": "推广中心",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "feedbackIndex/feedbackIndex",
				"style": {
					"navigationBarTitleText": "帮助中心",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "helpDetail/helpDetail",
				"style": {
					"navigationBarTitleText": "帮助详情",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "gird/guanzhu",
				"style": {
					"navigationBarTitleText": "我的粉丝",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "gird/visitor",
				"style": {
					"navigationBarTitleText": "最近访客",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "gird/browse",
				"style": {
					"navigationBarTitleText": "浏览足迹",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "wallet/wallet",
				"style": {
					"navigationBarTitleText": "提现"
				}
			},
			{
				"path": "wallet/index",
				"style": {
					"navigationBarTitleText": "我的钱包"
				}
			},

			{
				"path": "wallet/cashList",
				"style": {
					"navigationBarTitleText": "提现记录",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "wallet/mymoneydetail",
				"style": {
					"navigationBarTitleText": "钱包明细",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "wallet/zhifubao",
				"style": {
					"navigationBarTitleText": "提现账号"
				}
			},
			{
				"path": "order/pay",
				"style": {
					"navigationBarTitleText": "订单详情"
				}
			},
			{
				"path": "order/complain",
				"style": {
					"navigationBarTitleText": "投诉"
				}
			}, {
				"path": "order/tousuList",
				"style": {
					"navigationBarTitleText": "投诉记录"
				}
			},
			{
				"path": "setting/customer",
				"style": {
					"navigationBarTitleText": "在线客服"
				}
			},
			{
				"path": "feedback/index",
				"style": {
					"navigationBarTitleText": "意见反馈"
				}
			},
			{
				"path": "setting/help",
				"style": {
					"navigationBarTitleText": "帮助中心"
				}
			},
			{
				"path": "setting/index",
				"style": {
					"navigationBarTitleText": "设置中心"
				}
			},
			{
				"path": "setting/mimi",
				"style": {
					"navigationBarTitleText": "隐私政策"
				}
			},
			{
				"path": "setting/xieyi",
				"style": {
					"navigationBarTitleText": "用户协议"
				}
			},
			{
				"path": "setting/about",
				"style": {
					"navigationBarTitleText": "关于我们"
				}
			},
			{
				"path": "setting/userphone",
				"style": {
					"navigationBarTitleText": "修改手机号"
				}
			},
			{
				"path": "order/feedback",
				"style": {
					"navigationBarTitleText": "评价"
				}
			},
			{
				"path": "takeOrder/index",
				"style": {
					"navigationBarTitleText": "我的接单"
				}
			},
			{
				"path": "takeOrder/takeDetail",
				"style": {
					"navigationBarTitleText": "接单详情"
				}
			},
			{
				"path": "setting/chat",
				"style": {
					"navigationBarTitleText": "联系客服"
				}
			}, {
				"path": "other/agent",
				"style": {
					"navigationBarTitleText": "代理商"
					// "enablePullDownRefresh": true
				}
			}, {
				"path": "other/team",
				"style": {
					"navigationBarTitleText": "推广中心"
					// "enablePullDownRefresh": true
				}
			}, {
				"path": "other/commentList",
				"style": {
					"navigationBarTitleText": "用户评价",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "other/car",
				"style": {
					"navigationBarTitleText": "就诊人",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "other/addCar",
				"style": {
					"navigationBarTitleText": "添加就诊人信息"
				}
			}, {
				"path": "other/work",
				"style": {
					"navigationBarTitleText": "职务招聘"
				}
			}, {
				"path": "other/cooperation",
				"style": {
					"navigationBarTitleText": "招商加盟"
				}
			}, {
				"path": "other/index",
				"style": {
					"navigationBarTitleText": ""
				}
			}, {
				"path": "peizhen/peizhen",
				"style": {
					"navigationBarTitleText": "就医陪诊",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "yxpeizhen/yxpeizhen",
				"style": {
					"navigationBarTitleText": "码兄陪诊",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "ynpeihu/ynpeihu",
				"style": {
					"navigationBarTitleText": "院内陪护",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "peihu/order",
				"style": {
					"navigationBarTitleText": "填写订单",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "yxpeizhen/info",
				"style": {
					"navigationBarTitleText": "码兄陪诊",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "hospital/hospital",
				"style": {
					"navigationBarTitleText": "医院详情",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "xieyi/xieyi",
				"style": {
					"navigationBarTitleText": "服务条款同意书",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "address/address",
				"style": {
					"navigationBarTitleText": "地址管理",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "address/Endaddress",
				"style": {
					"navigationBarTitleText": "添加地址",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "city/city",
				"style": {
					"navigationBarTitleText": "服务城市",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "peizhenPeople/peizhenPeople",
				"style": {
					"navigationBarTitleText": "陪诊员",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "evaluation/evaluation",
				"style": {
					"navigationBarTitleText": "我的评价",
					"enablePullDownRefresh": true
				}

			}, {
				"path": "introduction/introduction",
				"style": {
					"navigationBarTitleText": "师傅介绍",
					"enablePullDownRefresh": false,
					"navigationBarBackgroundColor": "#468EF8",
					"navigationBarTextStyle": "white"
				}

			},
			{
				"path": "customServer/customServer",
				"style": {
					"navigationBarTitleText": "服务",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "customServer/customPz",
				"style": {
					"navigationBarTitleText": "填写订单",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "customServer/customPeop",
				"style": {
					"navigationBarTitleText": "护理员",
					"enablePullDownRefresh": true
				}
			},
			{
				"path": "customServer/customRem",
				"style": {
					"navigationBarTitleText": "师傅介绍",
					"enablePullDownRefresh": false,
					"navigationBarBackgroundColor": "#468EF8",
					"navigationBarTextStyle": "white"
				}
			},
			{
				"path": "customServer/customPeihu",
				"style": {
					"navigationBarTitleText": "填写订单",
					"enablePullDownRefresh": false
				}
			}
		]
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "陪诊",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#ffffff"
	},
	"tabBar": {
		"color": "#999999",
		"selectedColor": "#557EFD",
		"backgroundColor": "#FFFFFF",
		"borderStyle": "black",
		"list": [{
				"pagePath": "pages/index/index",
				"iconPath": "static/tabbar/index_.png",
				"selectedIconPath": "static/tabbar/index.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/peizhenPeop/peizhenPeop",
				"iconPath": "static/tabbar/peoples_.png",
				"selectedIconPath": "static/tabbar/peoples.png",
				"text": "陪诊师"
			},
			{
				"pagePath": "pages/order/index",
				"iconPath": "static/tabbar/order_.png",
				"selectedIconPath": "static/tabbar/order.png",
				"text": "订单"
			},
			{
				"pagePath": "pages/my/index",
				"iconPath": "static/tabbar/my_.png",
				"selectedIconPath": "static/tabbar/my.png",
				"text": "我的"
			}
		]
	}
}