<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="健康记录列表" name="first">
				<div style="display: flex;justify-content: space-between;align-items: center">
					<div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>患者姓名：</span>
							<el-input style="width: 200px;" @keydown.enter.native="select" placeholder="请输入患者姓名"
								v-model="patientName"></el-input>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>记录类型：</span>
							<el-select v-model="recordType" placeholder="请选择记录类型" style="width: 200px;" clearable>
								<el-option v-for="(item,index) in recordTypeList" :key="index" :label="item.value" :value="parseInt(item.code)"></el-option>
							</el-select>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>健康状态：</span>
							<el-select v-model="healthStatus" placeholder="请选择健康状态" style="width: 200px;" clearable>
								<el-option label="优秀" :value="1"></el-option>
								<el-option label="良好" :value="2"></el-option>
								<el-option label="一般" :value="3"></el-option>
								<el-option label="较差" :value="4"></el-option>
								<el-option label="差" :value="5"></el-option>
							</el-select>&nbsp;&nbsp;
						</div>
						<div style="position: relative;display: inline-block;margin: 3px;">
							<span>记录日期：</span>
							<el-date-picker v-model="recordDate" type="date" placeholder="请选择记录日期"
								style="width: 200px;" format="yyyy-MM-dd" value-format="yyyy-MM-dd" clearable></el-date-picker>&nbsp;&nbsp;
						</div>
						<br>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="select">
							查询
						</el-button>
						<el-button style="margin-left:15px;" size="mini" type="primary" icon="document" @click="cleans">
							重置
						</el-button>
						<el-button style='margin: 10px 0;' size="mini"
							type="primary" icon="document" @click="addHealthRecord()">添加健康记录
						</el-button>
						<el-button v-if="patientId" style='margin: 10px 0;' size="mini"
							type="info" icon="document" @click="goBack">返回患者列表
						</el-button>
					</div>
				</div>
				<el-table v-loading="tableDataLoading" :data="tableData.records">
					<el-table-column fixed prop="recordId" label="记录ID" width="80">
					</el-table-column>
					<el-table-column prop="patientName" label="患者姓名" width="120">
					</el-table-column>
					<el-table-column prop="recordDate" label="记录日期" width="120">
					</el-table-column>
					<el-table-column prop="recordTime" label="记录时间" width="100">
					</el-table-column>
					<el-table-column prop="recordType" label="记录类型" width="100">
						<template slot-scope="scope">
							<span>{{ getRecordTypeText(scope.row.recordType) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="healthStatus" label="健康状态" width="100">
						<template slot-scope="scope">
							<span>{{ getHealthStatusText(scope.row.healthStatus) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="weight" label="体重(kg)" width="100">
					</el-table-column>
					<el-table-column prop="height" label="身高(cm)" width="100">
					</el-table-column>
					<el-table-column prop="bmi" label="BMI" width="80">
					</el-table-column>
					<el-table-column prop="bloodPressureSystolic" label="血压" width="120">
						<template slot-scope="scope">
							<span v-if="scope.row.bloodPressureSystolic && scope.row.bloodPressureDiastolic">
								{{ scope.row.bloodPressureSystolic }}/{{ scope.row.bloodPressureDiastolic }}
							</span>
						</template>
					</el-table-column>
					<el-table-column prop="heartRate" label="心率" width="80">
					</el-table-column>
					<el-table-column prop="bodyTemperature" label="体温(℃)" width="100">
					</el-table-column>
					<el-table-column prop="riskAssessment" label="风险评估" width="100">
						<template slot-scope="scope">
							<span>{{ getRiskAssessmentText(scope.row.riskAssessment) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop="isAbnormal" label="是否异常" width="100">
						<template slot-scope="scope">
							<el-tag :type="scope.row.isAbnormal === 1 ? 'danger' : 'success'">
								{{ scope.row.isAbnormal === 1 ? '异常' : '正常' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="createTime" label="创建时间" width="160">
					</el-table-column>

					<el-table-column label="操作" width="200" fixed="right">
						<template slot-scope="scope">
							<el-button size="mini" type="primary" :disabled="!isAuth('healthRecord:view')"
								@click="viewHealthRecord(scope.row)" style="margin: 2px;">详情
							</el-button>
							<el-button size="mini" type="warning" :disabled="!isAuth('healthRecord:update')"
								@click="addHealthRecord(scope.row)" style="margin: 2px;">修改
							</el-button>
							<el-button size="mini" type="danger" :disabled="!isAuth('healthRecord:delete')"
								@click="deletes(scope.row)" style="margin: 2px;">删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
				<div style="text-align: center;margin-top: 10px;">
					<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:page-sizes="[10, 20, 30, 40]" :page-size="limit" :current-page="page"
						layout="total,sizes, prev, pager, next,jumper" :total="tableData.total">
					</el-pagination>
				</div>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				activeName: 'first',
				page: 1,
				limit: 10,
				tableData: {},
				tableDataLoading: false,
				patientId: '',
				patientName: '',
				recordType: '',
				healthStatus: '',
				recordDate: '',
				recordTypeList: [], // 记录类型字典
			}
		},
		methods: {
			handleClick(tab) {
				if (tab._props.label == '健康记录列表') {
					this.dataSelect()
				}
			},

			// 获取记录类型字典
			getRecordTypeList() {
				this.$http({
					url: this.$http.adornUrl('sys/dict/selectDictList'),
					method: 'get',
					params: this.$http.adornParams({
						'type': '记录类型'
					})
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.recordTypeList = data.data
					}
				})
			},

			// 获取数据列表
			dataSelect() {
				this.tableDataLoading = true
				let params = {
					'page': this.page,
					'limit': this.limit,
					'patientName': this.patientName,
					'recordType': this.recordType,
					'healthStatus': this.healthStatus,
					'recordDate': this.recordDate
				}
				
				// 如果有患者ID，添加到查询参数
				if (this.patientId) {
					params.patientId = this.patientId
				}

				this.$http({
					url: this.$http.adornUrl('admin/healthRecord/getHealthRecordList'),
					method: 'get',
					params: this.$http.adornParams(params)
				}).then(({
					data
				}) => {
					if (data && data.code === 0) {
						this.tableDataLoading = false
						let returnData = data.data;
						this.tableData = returnData;
					}
				})
			},

			select() {
				this.page = 1
				this.dataSelect()
			},

			cleans() {
				this.page = 1
				this.patientName = ''
				this.recordType = ''
				this.healthStatus = ''
				this.recordDate = ''
				this.dataSelect()
			},

			handleCurrentChange(val) {
				this.page = val
				this.dataSelect()
			},

			handleSizeChange(val) {
				this.limit = val
				this.dataSelect()
			},

			// 添加/修改健康记录
			addHealthRecord(row) {
				// 跳转到健康记录编辑页面
				let query = {}
				if (row) {
					query.recordId = row.recordId
				}
				if (this.patientId) {
					query.patientId = this.patientId
				}
				this.$router.push({
					path: '/healthRecordEdit',
					query: query
				})
			},

			// 查看健康记录详情
			viewHealthRecord(row) {
				this.$router.push({
					path: '/healthRecordDetail',
					query: {
						recordId: row.recordId
					}
				})
			},

			// 删除健康记录
			deletes(row) {
				this.$confirm(`确定删除此条健康记录?`, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				}).then(() => {
					this.$http({
						url: this.$http.adornUrl('admin/healthRecord/deleteHealthRecord'),
						method: 'get',
						params: this.$http.adornParams({
							recordId: row.recordId,
							operatorId: 1 // 这里应该是当前登录管理员的ID
						})
					}).then(({
						data
					}) => {
						if (data.code == 0) {
							this.$message({
								message: '删除成功',
								type: 'success',
								duration: 1500,
								onClose: () => {
									this.dataSelect()
								}
							})
						} else {
							this.$message({
								message: data.msg,
								type: 'warning',
								duration: 1500
							})
						}
					})
				}).catch(() => {})
			},

			// 返回患者列表
			goBack() {
				this.$router.push({
					path: '/carBrandList'
				})
			},

			// 获取记录类型文本
			getRecordTypeText(code) {
				if (!code) return ''
				const item = this.recordTypeList.find(item => item.code == code)
				return item ? item.value : ''
			},

			// 获取健康状态文本
			getHealthStatusText(code) {
				const statusMap = {
					1: '优秀',
					2: '良好',
					3: '一般',
					4: '较差',
					5: '差'
				}
				return statusMap[code] || ''
			},

			// 获取风险评估文本
			getRiskAssessmentText(code) {
				const riskMap = {
					1: '低风险',
					2: '中风险',
					3: '高风险'
				}
				return riskMap[code] || ''
			},
		},

		mounted() {
			// 获取路由参数
			this.patientId = this.$route.query.patientId || ''
			this.patientName = this.$route.query.patientName || ''
			
			this.dataSelect()
			this.getRecordTypeList()
		}
	}
</script>

<style>
</style>
