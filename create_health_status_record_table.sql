-- 创建健康状况记录表
CREATE TABLE `health_status_record` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `patient_id` bigint(20) DEFAULT NULL COMMENT '患者ID，关联patient_info表',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `record_date` date DEFAULT NULL COMMENT '记录日期',
  `record_time` time DEFAULT NULL COMMENT '记录时间',
  `record_type` int(11) DEFAULT '1' COMMENT '记录类型(1日常记录 2体检记录 3就诊记录 4复查记录 5紧急记录)',
  `health_status` int(11) DEFAULT '2' COMMENT '健康状态(1优秀 2良好 3一般 4较差 5差)',
  `symptoms` text COMMENT '症状描述',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
  `bmi` decimal(4,2) DEFAULT NULL COMMENT 'BMI指数',
  `blood_pressure_systolic` int(11) DEFAULT NULL COMMENT '收缩压(mmHg)',
  `blood_pressure_diastolic` int(11) DEFAULT NULL COMMENT '舒张压(mmHg)',
  `heart_rate` int(11) DEFAULT NULL COMMENT '心率(次/分)',
  `body_temperature` decimal(3,1) DEFAULT NULL COMMENT '体温(℃)',
  `blood_sugar` decimal(4,1) DEFAULT NULL COMMENT '血糖(mmol/L)',
  `medication_taken` text COMMENT '当日用药情况',
  `medication_adherence` int(11) DEFAULT NULL COMMENT '用药依从性(1完全依从 2基本依从 3部分依从 4不依从)',
  `exercise_duration` int(11) DEFAULT NULL COMMENT '运动时长(分钟)',
  `exercise_type` varchar(100) DEFAULT NULL COMMENT '运动类型',
  `sleep_hours` decimal(3,1) DEFAULT NULL COMMENT '睡眠时长(小时)',
  `sleep_quality` int(11) DEFAULT NULL COMMENT '睡眠质量(1很好 2好 3一般 4差 5很差)',
  `mood_status` int(11) DEFAULT NULL COMMENT '情绪状态(1很好 2好 3一般 4差 5很差)',
  `pain_level` int(11) DEFAULT '0' COMMENT '疼痛等级(0无痛 1-10级)',
  `pain_location` varchar(200) DEFAULT NULL COMMENT '疼痛部位',
  `diet_notes` text COMMENT '饮食记录',
  `special_events` text COMMENT '特殊事件记录',
  `doctor_advice` text COMMENT '医生建议',
  `next_followup_date` date DEFAULT NULL COMMENT '下次随访日期',
  `risk_assessment` int(11) DEFAULT '1' COMMENT '风险评估(1低风险 2中风险 3高风险)',
  `data_source` int(11) DEFAULT '1' COMMENT '数据来源(1手动录入 2设备同步 3医院导入 4第三方接口)',
  `device_info` varchar(200) DEFAULT NULL COMMENT '设备信息(如果是设备同步)',
  `is_abnormal` int(11) DEFAULT '0' COMMENT '是否异常(0正常 1异常)',
  `abnormal_indicators` text COMMENT '异常指标说明',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  `is_delete` int(11) DEFAULT '0' COMMENT '是否删除(0未删除 1已删除)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_record_date` (`record_date`),
  KEY `idx_record_type` (`record_type`),
  KEY `idx_health_status` (`health_status`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='健康状况记录表';

-- 插入一些字典数据（如果字典表存在的话）
-- 记录类型
INSERT IGNORE INTO `sys_dict` (`type`, `code`, `value`, `order_num`, `remark`) VALUES
('记录类型', '1', '日常记录', 1, '日常健康记录'),
('记录类型', '2', '体检记录', 2, '体检健康记录'),
('记录类型', '3', '就诊记录', 3, '就诊健康记录'),
('记录类型', '4', '复查记录', 4, '复查健康记录'),
('记录类型', '5', '紧急记录', 5, '紧急健康记录');

-- 睡眠质量
INSERT IGNORE INTO `sys_dict` (`type`, `code`, `value`, `order_num`, `remark`) VALUES
('睡眠质量', '1', '很好', 1, '睡眠质量很好'),
('睡眠质量', '2', '好', 2, '睡眠质量好'),
('睡眠质量', '3', '一般', 3, '睡眠质量一般'),
('睡眠质量', '4', '差', 4, '睡眠质量差'),
('睡眠质量', '5', '很差', 5, '睡眠质量很差');

-- 情绪状态
INSERT IGNORE INTO `sys_dict` (`type`, `code`, `value`, `order_num`, `remark`) VALUES
('情绪状态', '1', '很好', 1, '情绪状态很好'),
('情绪状态', '2', '好', 2, '情绪状态好'),
('情绪状态', '3', '一般', 3, '情绪状态一般'),
('情绪状态', '4', '差', 4, '情绪状态差'),
('情绪状态', '5', '很差', 5, '情绪状态很差');

-- 用药依从性
INSERT IGNORE INTO `sys_dict` (`type`, `code`, `value`, `order_num`, `remark`) VALUES
('用药依从性', '1', '完全依从', 1, '完全按医嘱用药'),
('用药依从性', '2', '基本依从', 2, '基本按医嘱用药'),
('用药依从性', '3', '部分依从', 3, '部分按医嘱用药'),
('用药依从性', '4', '不依从', 4, '不按医嘱用药');

-- 数据来源
INSERT IGNORE INTO `sys_dict` (`type`, `code`, `value`, `order_num`, `remark`) VALUES
('数据来源', '1', '手动录入', 1, '手动录入数据'),
('数据来源', '2', '设备同步', 2, '设备同步数据'),
('数据来源', '3', '医院导入', 3, '医院导入数据'),
('数据来源', '4', '第三方接口', 4, '第三方接口数据');
